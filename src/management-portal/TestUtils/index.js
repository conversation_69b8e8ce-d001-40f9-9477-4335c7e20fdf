import React from "react";
import { ThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import { act } from "react-dom/test-utils";
import { applyMiddleware, combineReducers, createStore } from "redux";
import { Provider } from "react-redux";
import cashierReducer from "../src/state/slices/cashier/cashier";
import poeReducer from "../src/state/slices/POE/payOnEntry";
import userReducer from "../src/state/slices/user";
import entityReducer from "../src/state/slices/entities";
import thunk from "redux-thunk";
import { render, cleanup } from "@testing-library/react";
import callCenterReducer from "../src/state/slices/callcenter";
import shiftSessionReducer from "../src/state/slices/shiftSession/shiftSession.js";
import peripheralsReducer from "../src/state/slices/peripherals.js";
import countryReducer from "../src/state/slices/countries.js";
import entityScopeReducer from "../src/state/slices/entityScope.js";
import activityReducer from "../src/state/slices/filters/activity.js";
import coreEntityReducer from "../src/state/slices/CoreEntity/CoreEntity.js";
import { renderHook } from "@testing-library/react-hooks";
import { useForm, FormProvider } from "react-hook-form";

export const actImmediate = (wrapper) =>
  act(
    () =>
      new Promise((resolve) => {
        setImmediate(() => {
          wrapper.update();
          resolve();
        });
      })
  );

  const theme = createMuiTheme({
    palette: {
        amano: {
            warnings: {
                warning:  "#123456",
            },
        },
    },
  });

export const waitForComponentRender = async (wrapper) => {
  await act(async () => {
    await new Promise((resolve) => setTimeout(resolve, 0));
    wrapper.update();
  });
};

export const renderWithRedux = (ui, mockState) => {
  // name of reducer must match name defined in slice registration
  const store = createStore(
    combineReducers({
      payOnEntry: poeReducer,
      entities: entityReducer,
      user: userReducer,
      cashier: cashierReducer,
      callcenter: callCenterReducer,
      shiftSession: shiftSessionReducer,
      peripherals: peripheralsReducer,
      countries: countryReducer,
      entityScope: entityScopeReducer,
      activity: activityReducer,
      coreEntities: coreEntityReducer,
    }),
    mockState || {},
    applyMiddleware(thunk)
  );

  return render(<Provider store={store}>{ui}</Provider>);
};

export const renderWithReduxAndTheme = (ui, mockState) => {
  // name of reducer must match name defined in slice registration
  const store = createStore(
    combineReducers({
      payOnEntry: poeReducer,
      entities: entityReducer,
      user: userReducer,
      cashier: cashierReducer,
      callcenter: callCenterReducer,
      shiftSession: shiftSessionReducer,
      peripherals: peripheralsReducer,
      countries: countryReducer,
      entityScope: entityScopeReducer,
      activity: activityReducer,
      coreEntities: coreEntityReducer,
    }),
    mockState || {},
    applyMiddleware(thunk)
  );
  
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        {ui}
      </ThemeProvider>
    </Provider>
  );
};

function getWrapper(store) {
  return ({ children }) => <Provider store={store}>{children}</Provider>;
}

export const renderHookWithRedux = (hook, mockState) => {
  // name of reducer must match name defined in slice registration
  const store = createStore(
    combineReducers({
      payOnEntry: poeReducer,
      entities: entityReducer,
      user: userReducer,
      cashier: cashierReducer,
      callcenter: callCenterReducer,
      shiftSession: shiftSessionReducer,
      peripherals: peripheralsReducer,
      countries: countryReducer,
      entityScope: entityScopeReducer,
      activity: activityReducer
    }),
    mockState || {},
    applyMiddleware(thunk)
  );

  const wrapper = getWrapper(store);


  return renderHook(hook, { wrapper });
};

export const withMarkup = (query) => (text) =>
  query((content, node) => {
    const hasText = (node) => node.textContent === text;
    const childrenDontHaveText = Array.from(node.children).every(
      (child) => !hasText(child)
    );
    return hasText(node) && childrenDontHaveText;
  });

export const FormProviderWrapper = ({ children, defaultValues = {} }) => {
  const methods = useForm({ defaultValues });
  const content = typeof children === "function" ? children(methods) : children;
  return <FormProvider {...methods}>{content}</FormProvider>;
};